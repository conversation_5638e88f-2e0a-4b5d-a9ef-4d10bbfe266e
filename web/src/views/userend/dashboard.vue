<template>
    <div class="dominos-order-container">

        <div class="main-content">
            <!-- 第一层级区域 - 下单信息、快速识别地址 左右布局 -->
            <div class="content-wrapper">
                <!-- 左侧：填写下单信息 -->
                <div class="left-panel">
                <div class="order-form">
                    <h3 class="section-title">填写下单信息</h3>
                    <el-form :model="orderForm" label-width="80px" class="form-content">
                        <!-- 送餐城市 -->
                        <el-form-item label="送餐城市:">
                            <el-select
                                v-model="orderForm.deliveryCity"
                                placeholder="上海市"
                                class="form-input"
                                clearable
                            >
                                <el-option label="上海市" value="shanghai" />
                                <el-option label="北京市" value="beijing" />
                                <el-option label="广州市" value="guangzhou" />
                                <el-option label="深圳市" value="shenzhen" />
                            </el-select>
                        </el-form-item>

                        <!-- 送餐地址 -->
                        <el-form-item label="送餐地址:">
                            <div class="delivery-address-group">
                                <el-input
                                    v-model="orderForm.deliveryAddress"
                                    placeholder="地址或小区写字楼或学校"
                                    class="delivery-input"
                                />
                                <el-button type="primary" class="search-btn">搜索</el-button>
                            </div>
                        </el-form-item>

                        <!-- 详细地址 -->
                        <el-form-item label="详细地址:">
                            <el-input
                                v-model="orderForm.detailAddress"
                                placeholder="楼号/门牌号，例：1号101室"
                                class="form-input"
                            />
                        </el-form-item>



                        <!-- 收货人 -->
                        <el-form-item label="*收货人:">
                            <el-input
                                v-model="orderForm.receiverName"
                                placeholder="请输入收货人姓名"
                                class="form-input"
                            />
                        </el-form-item>

                        <!-- 联系电话 -->
                        <el-form-item label="*联系电话:">
                            <el-input
                                v-model="orderForm.phone"
                                placeholder="请输入联系电话"
                                class="form-input"
                            />
                        </el-form-item>

                        <!-- 送餐门店 -->
                        <el-form-item label="*送餐门店:">
                            <el-input
                                v-model="orderForm.store"
                                placeholder="请选择门店"
                                class="form-input"
                                readonly
                            />
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 右侧：快速识别地址 -->
            <div class="right-panel">
                <div class="address-recognition">
                    <!-- 提示信息 -->
                    <div class="tip-text">
                        粘贴收件人姓名、手机号、收货地址(要包含省市区)，才可快速识别收货信息。
                    </div>

                    <!-- 识别文本区域 -->
                    <div class="recognition-area">
                        <el-input
                            v-model="recognitionText"
                            type="textarea"
                            :rows="8"
                            placeholder="请粘贴收件人信息..."
                            class="recognition-textarea"
                        />
                    </div>

                    <!-- 操作按钮 -->
                    <div class="recognition-actions">
                        <span class="clear-text" @click="clearRecognition">清除</span>
                        <el-button type="primary" @click="recognizeAddress">一键识别</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'

// 订单表单数据
const orderForm = reactive({
    deliveryCity: 'shanghai',
    deliveryAddress: '',
    detailAddress: '',
    receiverName: '',
    phone: '',
    store: ''
})

// 识别文本
const recognitionText = ref('')

// 添加客户功能
const addCustomer = () => {
    console.log('添加客户:', orderForm.customerName)
    // TODO: 实现添加客户逻辑
}

// 搜索地址功能
const searchAddress = () => {
    console.log('搜索地址:', orderForm.deliveryAddress)
    // TODO: 实现地址搜索逻辑
}

// 清除识别文本
const clearRecognition = () => {
    recognitionText.value = ''
}

// 一键识别地址
const recognizeAddress = () => {
    if (!recognitionText.value.trim()) {
        return
    }

    // 简单的地址识别逻辑（可以根据实际需求完善）
    const text = recognitionText.value

    // 识别手机号
    const phoneMatch = text.match(/1[3-9]\d{9}/)
    if (phoneMatch) {
        orderForm.phone = phoneMatch[0]
    }

    // 识别姓名（假设姓名在手机号前面，2-4个字符）
    const nameMatch = text.match(/([^\d\s]{2,4})\s*1[3-9]\d{9}/)
    if (nameMatch) {
        orderForm.customerName = nameMatch[1]
    }

    // 识别地址（包含省市区的部分）
    const addressMatch = text.match(/([\u4e00-\u9fa5]+[省市区县][\u4e00-\u9fa5\d\s]+)/)
    if (addressMatch) {
        orderForm.deliveryAddress = addressMatch[1]
    }

    // 识别收货人姓名（假设姓名在手机号前面，2-4个字符）
    if (nameMatch) {
        orderForm.receiverName = nameMatch[1]
    }

    console.log('识别结果:', orderForm)
}
</script>

<style scoped lang="scss">
.dominos-order-container {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;



    .main-content {
        display: flex;
        justify-content: center;
        align-items: flex-start;

        .section-title {
            margin: 0 0 24px 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
            position: relative;
            padding-left: 12px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 20px;
                background: linear-gradient(135deg, #409eff, #67c23a);
                border-radius: 2px;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: -8px;
                left: 12px;
                width: 40px;
                height: 2px;
                background: linear-gradient(90deg, #409eff, transparent);
                border-radius: 1px;
            }
        }

        .content-wrapper {
            display: flex;
            gap: 24px;
            align-items: stretch;
            width: 100%;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 1px;
                height: 100%;
                background: linear-gradient(to bottom, transparent, #e0e6ed 20%, #e0e6ed 80%, transparent);
                z-index: 1;
            }
        }

        .left-panel {
            flex: 1;
            width: 50%;
            min-width: 400px;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 2;

            .order-form {
                background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
                padding: 30px;
                border-radius: 12px;
                box-shadow:
                    0 4px 20px rgba(0, 0, 0, 0.08),
                    0 1px 3px rgba(0, 0, 0, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.8);
                flex: 1;
                display: flex;
                flex-direction: column;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #409eff, #67c23a, #409eff);
                    background-size: 200% 100%;
                    animation: shimmer 3s ease-in-out infinite;
                }

                &:hover {
                    transform: translateY(-2px);
                    box-shadow:
                        0 8px 30px rgba(0, 0, 0, 0.12),
                        0 2px 6px rgba(0, 0, 0, 0.08);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .form-title {
                    margin: 0 0 25px 0;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                }

                .form-content {
                    .el-form-item {
                        margin-bottom: 24px;
                        position: relative;

                        &:hover {
                            .el-form-item__label {
                                color: #409eff;
                                transition: color 0.2s ease;
                            }
                        }
                    }

                    .form-input {
                        width: 100%;

                        &:hover {
                            transform: translateY(-1px);
                            transition: transform 0.2s ease;
                        }
                    }

                    .delivery-address-group {
                        display: flex;
                        gap: 12px;
                        align-items: center;
                        padding: 4px;
                        border-radius: 8px;
                        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                        border: 1px solid #e9ecef;

                        &:hover {
                            border-color: #409eff;
                            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
                            transition: all 0.3s ease;
                        }

                        .delivery-input {
                            flex: 1;

                            :deep(.el-input__wrapper) {
                                border: none;
                                box-shadow: none;
                                background: transparent;
                            }
                        }

                        .search-btn {
                            flex-shrink: 0;
                            background: linear-gradient(135deg, #409eff, #5dade2);
                            border: none;

                            &:hover {
                                background: linear-gradient(135deg, #5dade2, #409eff);
                                transform: scale(1.05);
                                transition: all 0.2s ease;
                            }
                        }
                    }
                }
            }
        }

        .right-panel {
            flex: 1;
            width: 50%;
            min-width: 400px;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 2;

            .address-recognition {
                background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
                padding: 30px;
                border-radius: 12px;
                box-shadow:
                    0 4px 20px rgba(64, 158, 255, 0.08),
                    0 1px 3px rgba(64, 158, 255, 0.05);
                border: 1px solid rgba(64, 158, 255, 0.1);
                flex: 1;
                display: flex;
                flex-direction: column;
                position: relative;
                overflow: hidden;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #67c23a, #409eff, #67c23a);
                    background-size: 200% 100%;
                    animation: shimmer 3s ease-in-out infinite reverse;
                }

                &:hover {
                    transform: translateY(-2px);
                    box-shadow:
                        0 8px 30px rgba(64, 158, 255, 0.15),
                        0 2px 6px rgba(64, 158, 255, 0.1);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .tip-text {
                    color: #5a6c7d;
                    font-size: 14px;
                    line-height: 1.6;
                    margin-bottom: 24px;
                    padding: 18px 20px;
                    background: linear-gradient(135deg, #f0f7ff 0%, #e8f4fd 100%);
                    border-radius: 8px;
                    border-left: 4px solid #409eff;
                    position: relative;

                    &::before {
                        content: '💡';
                        position: absolute;
                        top: 18px;
                        right: 20px;
                        font-size: 16px;
                        opacity: 0.7;
                    }

                    &:hover {
                        background: linear-gradient(135deg, #e8f4fd 0%, #ddeeff 100%);
                        transform: translateX(2px);
                        transition: all 0.2s ease;
                    }
                }

                .recognition-area {
                    margin-bottom: 20px;

                    .recognition-textarea {
                        width: 100%;
                    }
                }

                .recognition-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 16px 20px;
                    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                    border-radius: 8px;
                    border: 1px solid #e9ecef;

                    .clear-text {
                        color: #ff6b7a;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        padding: 8px 16px;
                        border-radius: 6px;
                        transition: all 0.2s ease;

                        &:hover {
                            background: rgba(255, 107, 122, 0.1);
                            color: #ff4757;
                            transform: translateY(-1px);
                        }
                    }

                    .el-button {
                        background: linear-gradient(135deg, #67c23a, #85ce61);
                        border: none;
                        font-weight: 500;

                        &:hover {
                            background: linear-gradient(135deg, #85ce61, #67c23a);
                            transform: scale(1.05);
                            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
                        }
                    }
                }
            }
        }
    }

    // 响应式设计
    @media (max-width: 1000px) {
        .main-content {
            .content-wrapper {
                flex-direction: column;

                .left-panel,
                .right-panel {
                    max-width: none;
                    width: 100%;
                }
            }
        }
    }
}

// 动画效果
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Element Plus 样式覆盖
:deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
        border-color: #c0c4cc;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
}

:deep(.el-button) {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        transform: translateY(-1px);
    }
}

:deep(.el-select) {
    width: 100%;

    .el-input__wrapper {
        &:hover {
            border-color: #409eff;
        }
    }
}

:deep(.el-textarea__inner) {
    border-radius: 8px;
    resize: none;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
        border-color: #c0c4cc;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
}

:deep(.el-form-item__label) {
    font-weight: 500;
    color: #606266;
    transition: color 0.2s ease;
}
</style>
